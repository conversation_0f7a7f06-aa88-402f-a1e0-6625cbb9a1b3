import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class Main {
    public static void main(String[] args) {
        int n = 5;
        DiningPhilosophers1 dp = new DiningPhilosophers1(n);
        List<Thread> threads = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            PhilosophersThread pt = new PhilosophersThread(i, dp);
            threads.add(pt);
            pt.start();
        }

        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        for (int i = 0; i < n; i++) {
            threads.get(i).interrupt();
        }

        for (int i = 0; i < n; i++) {
            try {
                threads.get(i).join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }
}

class PhilosophersThread extends Thread {
    private int id;
    private DiningPhilosophers1 dp;

    public PhilosophersThread(int id, DiningPhilosophers1 dp) {
        this.id = id;
        this.dp = dp;
    }

    @Override
    public void run() {
        Random rd = new Random();
        while (true) {
            dp.getForks(id);
            // eating
            try {
                Thread.sleep(rd.nextInt(100));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } finally {
                dp.releaseForks(id);
            }
            // thinking
            System.out.println("Philosopher " + id + " is thinking");
            try {
                Thread.sleep(rd.nextInt(100));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
}
