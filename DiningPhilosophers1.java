import java.util.concurrent.locks.ReentrantLock;

public class DiningPhilosophers1 {
    private int n; // number of philosophers
    private ReentrantLock[] forks;

    public DiningPhilosophers1(int n) {
        this.n = n;
        forks = new ReentrantLock[n];
        for (int i = 0; i < n; i++) {
            forks[i] = new ReentrantLock();
        }
    }

    public void getForks(int p) {
        int leftFork = p;
        int rightFork = (p + 1) % n;
        
        if (p%2 == 0) {
            // Even philosophers: get right fork first, then left
            forks[rightFork].lock();
            System.out.println("Philosopher " + p + " got fork " + rightFork);
            forks[leftFork].lock();
            System.out.println("Philosopher " + p + " got fork " + leftFork);
            System.out.println("Philosopher " + p + " has both forks and can eat");
        } else {
            // Odd philosophers: get left fork first, then right
            forks[leftFork].lock();
            System.out.println("Philosopher " + p + " got fork " + leftFork);
            forks[rightFork].lock();
            System.out.println("Philosopher " + p + " got fork " + rightFork);
            System.out.println("Philosopher " + p + " has both forks and can eat");
        }
    }

    public void releaseForks(int p) {
        int leftFork = p;
        int rightFork = (p + 1) % n;
        
        forks[leftFork].unlock();
        forks[rightFork].unlock();
    }
}